require 'test_helper'

class AdminAccessControlTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create admin users for testing
    @superadmin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Super',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @readonly_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: '<PERSON>Only',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    # Create a regular user for testing
    @regular_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @regular_user, role: @scout_role)

    # Create organization and memberships
    @organization = Organization.create!(name: 'Test Access Organization')
    [@superadmin, @readonly_admin, @regular_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'superadmin can access admin dashboard' do
    sign_in_as(@superadmin)

    get super_admin_admin_dashboard_path
    assert_response :success
    assert_select 'h1', text: /admin dashboard/i
  end

  test 'superadmin can access admin users' do
    sign_in_as(@superadmin)

    # Can access admin users
    get super_admin_admin_users_path
    assert_response :success
  end

  test 'readonly admin can access dashboard but cannot edit users' do
    sign_in_as(@readonly_admin)

    # Can access admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success

    # Can access users index (has users_read permission)
    get super_admin_admin_users_path
    assert_response :success

    # Test that readonly admin cannot access edit functionality
    # This would require a separate test for edit permissions
  end

  test 'regular user cannot access admin interfaces' do
    sign_in_as(@regular_user)

    # Cannot access admin dashboard
    get super_admin_admin_dashboard_path
    assert_redirected_to root_path
    follow_redirect!
    assert_match /administrative privileges required/i, flash[:alert]

    # Cannot access users interface
    get super_admin_admin_users_path
    assert_redirected_to root_path
    follow_redirect!
    assert_match /administrative privileges required/i, flash[:alert]
  end

  test 'unauthenticated user cannot access admin interfaces' do
    # Try to access admin dashboard without signing in
    get super_admin_admin_dashboard_path
    assert_redirected_to sign_in_path

    # Try to access users interface without signing in
    get super_admin_admin_users_path
    assert_redirected_to sign_in_path
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
