<% content_for :title, "Conversations" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Conversations</h1>
    <p class="mt-2 text-stone-600">Manage conversations between users</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search by participant name or email...",
      filters: [
        { key: 'with_job', label: 'Job Association', options: @filter_options[:with_job] },
        { key: 'active', label: 'Activity', options: @filter_options[:active] },
        { key: 'recent', label: 'Recency', options: @filter_options[:recent] }
      ] %>

  <!-- Results Summary -->
  <div class="mb-4">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> conversations
    </p>
  </div>

  <!-- Conversations Table -->
  <%= render 'shared/admin/table', 
      collection: @conversations,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'participants', 
          label: 'Participants',
          render: ->(conversation) {
            content_tag(:div) do
              conversation.users.map do |user|
                concat content_tag(:div, user.name, class: "text-sm font-medium text-stone-900")
                concat content_tag(:div, user.email, class: "text-sm text-stone-500")
              end.join.html_safe
            end
          }
        },
        { 
          key: 'job', 
          label: 'Related Job',
          render: ->(conversation) {
            if conversation.job
              content_tag(:div) do
                concat content_tag(:div, truncate(conversation.job.title, length: 40), class: "text-sm font-medium text-stone-900")
                concat content_tag(:div, conversation.job.organization.name, class: "text-sm text-stone-500")
              end
            else
              content_tag(:span, "No job", class: "text-sm text-stone-500 italic")
            end
          }
        },
        { 
          key: 'messages_count', 
          label: 'Messages',
          render: ->(conversation) {
            count = conversation.messages.size
            content_tag(:div) do
              concat content_tag(:span, "#{count} messages", class: "text-sm text-stone-900")
              if count > 0
                last_message_time = conversation.messages.maximum(:created_at)
                concat content_tag(:div, "Last: #{time_ago_in_words(last_message_time)} ago", class: "text-xs text-stone-500")
              end
            end
          }
        },
        { 
          key: 'activity_status', 
          label: 'Activity',
          render: ->(conversation) {
            last_activity = conversation.messages.maximum(:created_at) || conversation.created_at
            is_recent = last_activity > 24.hours.ago
            
            status_class = is_recent ? 'bg-green-100 text-green-800' : 'bg-stone-100 text-stone-800'
            status_text = is_recent ? 'Active' : 'Inactive'
            
            content_tag(:div) do
              concat content_tag(:span, status_text, 
                class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_class}")
              concat content_tag(:div, "#{time_ago_in_words(last_activity)} ago", class: "text-xs text-stone-500 mt-1")
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true,
          render: ->(conversation) {
            content_tag(:div) do
              concat content_tag(:div, conversation.created_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
              concat content_tag(:div, conversation.created_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
            end
          }
        },
        { 
          key: 'updated_at', 
          label: 'Last Updated', 
          sortable: true,
          render: ->(conversation) {
            content_tag(:div) do
              concat content_tag(:div, conversation.updated_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
              concat content_tag(:div, conversation.updated_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
            end
          }
        }
      ],
      actions: true,
      show_path: ->(conversation) { super_admin_admin_conversation_path(conversation) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
