<% content_for :title, "Chat Requests" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Chat Requests</h1>
    <p class="mt-2 text-stone-600">Manage chat requests between scouts and talents</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search by scout name, talent name, email, or pitch...",
      filters: [
        { key: 'status', label: 'Status', options: @filter_options[:status] }
      ] %>

  <!-- Results Summary -->
  <div class="mb-4">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> chat requests
    </p>
  </div>

  <!-- Chat Requests Table -->
  <%= render 'shared/admin/table', 
      collection: @chat_requests,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'scout', 
          label: 'Scout',
          render: ->(chat_request) {
            content_tag(:div) do
              concat content_tag(:div, chat_request.scout.name, class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, chat_request.scout.email, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'talent', 
          label: 'Talent',
          render: ->(chat_request) {
            content_tag(:div) do
              concat content_tag(:div, chat_request.talent.name, class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, chat_request.talent.email, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'status', 
          label: 'Status', 
          sortable: true,
          render: ->(chat_request) {
            status_class = case chat_request.status
                          when 'accepted' then 'bg-green-100 text-green-800'
                          when 'pending' then 'bg-yellow-100 text-yellow-800'
                          when 'declined' then 'bg-red-100 text-red-800'
                          else 'bg-stone-100 text-stone-800'
                          end
            content_tag(:span, chat_request.status.humanize, 
              class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_class}")
          }
        },
        { 
          key: 'pitch', 
          label: 'Pitch',
          render: ->(chat_request) {
            if chat_request.pitch.present?
              content_tag(:div, truncate(chat_request.pitch, length: 100), class: "text-sm text-stone-900")
            else
              content_tag(:span, "No pitch", class: "text-sm text-stone-500 italic")
            end
          }
        },
        { 
          key: 'requested_at', 
          label: 'Requested', 
          sortable: true,
          render: ->(chat_request) {
            if chat_request.requested_at
              content_tag(:span, chat_request.requested_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
            else
              content_tag(:span, "Not set", class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'accepted_at', 
          label: 'Accepted', 
          sortable: true,
          render: ->(chat_request) {
            if chat_request.accepted_at
              content_tag(:span, chat_request.accepted_at.strftime('%b %d, %Y'), class: "text-sm text-green-600")
            else
              content_tag(:span, "-", class: "text-sm text-stone-400")
            end
          }
        },
        { 
          key: 'declined_at', 
          label: 'Declined', 
          sortable: true,
          render: ->(chat_request) {
            if chat_request.declined_at
              content_tag(:span, chat_request.declined_at.strftime('%b %d, %Y'), class: "text-sm text-red-600")
            else
              content_tag(:span, "-", class: "text-sm text-stone-400")
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true 
        }
      ],
      actions: true,
      show_path: ->(chat_request) { super_admin_admin_chat_request_path(chat_request) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
