<% content_for :title, "Users" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Users</h1>
    <p class="mt-2 text-stone-600">Manage user accounts and permissions</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search users by email, first name, or last name...",
      filters: [
        { key: 'verified', label: 'Verification Status', options: @filter_options[:verified] },
        { key: 'onboarding_completed', label: 'Onboarding Status', options: @filter_options[:onboarding_completed] },
        { key: 'role', label: 'Role', options: @filter_options[:role] }
      ] %>

  <!-- Results Summary and Export -->
  <div class="mb-4 flex justify-between items-center">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> users
    </p>
    <div class="flex space-x-2">
      <%= link_to "Export CSV", request.params.merge(format: :csv),
          class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>

  <!-- Users Table -->
  <%= render 'shared/admin/table', 
      collection: @users,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'avatar', 
          label: 'Avatar',
          render: ->(user) {
            if user.avatar.attached?
              image_tag(user.avatar, class: "h-8 w-8 rounded-full object-cover")
            else
              content_tag(:div, user.initials, class: "h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center text-xs font-medium text-stone-700")
            end
          }
        },
        { 
          key: 'email', 
          label: 'Email', 
          sortable: true,
          render: ->(user) {
            content_tag(:div) do
              concat content_tag(:div, user.email, class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, user.name, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'roles', 
          label: 'Roles',
          render: ->(user) {
            if user.roles.any?
              user.roles.map(&:name).map { |role|
                content_tag(:span, role.humanize, 
                  class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1")
              }.join.html_safe
            else
              content_tag(:span, "No roles", class: "text-stone-400 text-sm")
            end
          }
        },
        { 
          key: 'verified', 
          label: 'Verified', 
          sortable: true 
        },
        { 
          key: 'onboarding_completed', 
          label: 'Onboarding', 
          sortable: true,
          render: ->(user) {
            if user.onboarding_completed?
              content_tag(:span, "Complete", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800")
            else
              content_tag(:span, user.onboarding_step&.humanize || "Not started", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800")
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true 
        }
      ],
      actions: true,
      show_path: ->(user) { super_admin_admin_user_path(user) },
      edit_path: ->(user) { edit_super_admin_admin_user_path(user) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
