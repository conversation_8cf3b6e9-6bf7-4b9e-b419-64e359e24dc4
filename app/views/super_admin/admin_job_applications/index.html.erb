<% content_for :title, "Job Applications" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Job Applications</h1>
    <p class="mt-2 text-stone-600">Manage all job applications across the platform</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search by applicant name, email, job title, or application letter...",
      filters: [
        { key: 'status', label: 'Status', options: @filter_options[:status] },
        { key: 'invited', label: 'Invitation Status', options: @filter_options[:invited] },
        { key: 'salary_considered', label: 'Salary Consideration', options: @filter_options[:salary_considered] }
      ] %>

  <!-- Results Summary -->
  <div class="mb-4">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> job applications
    </p>
  </div>

  <!-- Job Applications Table -->
  <%= render 'shared/admin/table', 
      collection: @job_applications,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'applicant', 
          label: 'Applicant',
          render: ->(application) {
            content_tag(:div) do
              concat content_tag(:div, application.user.name.full, class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, application.user.email, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'job', 
          label: 'Job',
          render: ->(application) {
            content_tag(:div) do
              concat link_to(truncate(application.job.title, length: 40), 
                            super_admin_admin_job_path(application.job), 
                            class: "text-sm font-medium text-blue-600 hover:text-blue-900")
              concat content_tag(:div, application.job.job_category.humanize, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'status', 
          label: 'Status', 
          sortable: true,
          render: ->(application) {
            status_class = case application.status
                          when 'applied' then 'bg-blue-100 text-blue-800'
                          when 'reviewed' then 'bg-yellow-100 text-yellow-800'
                          when 'qualified' then 'bg-green-100 text-green-800'
                          when 'offered' then 'bg-purple-100 text-purple-800'
                          when 'accepted' then 'bg-emerald-100 text-emerald-800'
                          when 'withdrawn' then 'bg-red-100 text-red-800'
                          else 'bg-stone-100 text-stone-800'
                          end
            content_tag(:span, application.status.humanize, 
              class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_class}")
          }
        },
        { 
          key: 'invitation_status', 
          label: 'Invitation',
          render: ->(application) {
            if application.invited?
              content_tag(:div) do
                concat content_tag(:span, "Invited", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800")
                if application.job_invitation.present?
                  concat content_tag(:div, "Via invitation", class: "text-xs text-stone-500 mt-1")
                end
              end
            else
              content_tag(:span, "Direct Application", class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'applied_at', 
          label: 'Applied', 
          sortable: true,
          render: ->(application) {
            if application.applied_at.present?
              content_tag(:div) do
                concat content_tag(:div, application.applied_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
                concat content_tag(:div, application.applied_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
              end
            else
              content_tag(:span, "Not set", class: "text-sm text-stone-500 italic")
            end
          }
        },
        { 
          key: 'salary_considered', 
          label: 'Salary',
          render: ->(application) {
            if application.salary_considered?
              content_tag(:span, "Considered", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800")
            else
              content_tag(:span, "Not Considered", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800")
            end
          }
        },
        { 
          key: 'attachments', 
          label: 'Attachments',
          render: ->(application) {
            attachments = []
            attachments << "Resume" if application.resume.attached?
            attachments << "#{application.documents.count} docs" if application.documents.any?
            
            if attachments.any?
              content_tag(:div, attachments.join(", "), class: "text-sm text-stone-900")
            else
              content_tag(:span, "None", class: "text-sm text-stone-500 italic")
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true,
          render: ->(application) {
            content_tag(:div) do
              concat content_tag(:div, application.created_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
              concat content_tag(:div, application.created_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
            end
          }
        }
      ],
      actions: true,
      show_path: ->(application) { super_admin_admin_job_application_path(application) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
