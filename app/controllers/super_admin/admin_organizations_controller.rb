class SuperAdmin::AdminOrganizationsController < SuperAdmin::AdminBaseController
  before_action :set_organization, only: %i[show edit update]

  def index
    @organizations = Organization.includes(:jobs, organization_memberships: :user)

    # Apply search
    @organizations = apply_search(@organizations)

    # Apply filters
    if params[:with_jobs] == 'true'
      @organizations = @organizations.joins(:jobs).distinct
    elsif params[:with_jobs] == 'false'
      @organizations = @organizations.left_joins(:jobs).where(jobs: { id: nil })
    end

    if params[:size].present?
      @organizations = @organizations.where(size: params[:size])
    end

    # Date filters
    if params[:created_after].present?
      @organizations = @organizations.where('created_at >= ?', Date.parse(params[:created_after]))
    end
    if params[:created_before].present?
      @organizations = @organizations.where('created_at <= ?', Date.parse(params[:created_before]))
    end

    # Apply sorting
    @organizations = apply_sorting(
      @organizations,
      %w[id name size created_at updated_at]
    )

    # Paginate
    @pagy, @organizations = pagy(@organizations, items: @page_size)

    # Set up filter options
    @filter_options = {
      with_jobs: [['With Jobs', 'true'], ['Without Jobs', 'false']],
      size: [
        ['Startup (1-10)', 'startup'],
        ['Small (11-50)', 'small'],
        ['Medium (51-200)', 'medium'],
        ['Large (201-1000)', 'large'],
        ['Enterprise (1000+)', 'enterprise']
      ]
    }
  end

  def show
    @members = @organization.organization_memberships.includes(:user)
    @recent_jobs = @organization.jobs.order(created_at: :desc).limit(10)
    @stats = {
      total_jobs: @organization.jobs.count,
      published_jobs: @organization.jobs.where(status: 'published').count,
      draft_jobs: @organization.jobs.where(status: 'draft').count,
      members_count: @organization.organization_memberships.count,
      owners_count: @organization.organization_memberships.where(org_role: 'owner').count,
      admins_count: @organization.organization_memberships.where(org_role: 'admin').count
    }
  end

  def edit
    # @organization is set by set_organization
  end

  def update
    if @organization.update(organization_params)
      redirect_to super_admin_admin_organization_path(@organization), 
                  notice: 'Organization was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_organization
    @organization = Organization.find(params[:id])
  end

  def organization_params
    params.require(:organization).permit(:name, :size, :operating_timezone)
  end

  def apply_search(collection)
    return collection unless @search.present?

    search_term = "%#{@search}%"
    collection.where("name ILIKE ?", search_term)
  end
end
