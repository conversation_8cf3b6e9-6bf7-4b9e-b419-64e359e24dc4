class SuperAdmin::AdminJobApplicationsController < SuperAdmin::AdminBaseController
  before_action :set_job_application, only: %i[show]

  def index
    @job_applications = JobApplication.includes(:job, :user, :job_invitation)

    # Apply search
    @job_applications = apply_search_with_associations(@job_applications)

    # Apply filters
    @job_applications = @job_applications.where(status: params[:status]) if params[:status].present?
    
    if params[:invited] == 'true'
      @job_applications = @job_applications.where(invited: true)
    elsif params[:invited] == 'false'
      @job_applications = @job_applications.where(invited: false)
    end

    if params[:salary_considered] == 'true'
      @job_applications = @job_applications.where(salary_considered: true)
    elsif params[:salary_considered] == 'false'
      @job_applications = @job_applications.where(salary_considered: false)
    end

    # Date filters
    if params[:applied_after].present?
      @job_applications = @job_applications.where('applied_at >= ?', Date.parse(params[:applied_after]))
    end
    if params[:applied_before].present?
      @job_applications = @job_applications.where('applied_at <= ?', Date.parse(params[:applied_before]))
    end

    # Apply sorting
    @job_applications = apply_sorting(
      @job_applications,
      %w[id status applied_at accepted_at rejected_at created_at]
    )

    # Paginate
    @pagy, @job_applications = pagy(@job_applications, items: @page_size)

    # Set up filter options
    @filter_options = {
      status: JobApplication.statuses.map { |key, value| [key.humanize, key] },
      invited: [['Invited', 'true'], ['Not Invited', 'false']],
      salary_considered: [['Salary Considered', 'true'], ['Salary Not Considered', 'false']]
    }
  end

  def show
    @related_applications = JobApplication.where(user: @job_application.user)
                                          .where.not(id: @job_application.id)
                                          .includes(:job)
                                          .order(created_at: :desc)
                                          .limit(5)
  end

  private

  def set_job_application
    @job_application = JobApplication.find(params[:id])
  end

  def apply_search_with_associations(collection)
    return collection unless @search.present?

    search_term = "%#{@search}%"
    collection.joins(:user, :job)
              .where(
                "users.first_name ILIKE :search OR users.last_name ILIKE :search OR users.email ILIKE :search OR 
                 jobs.title ILIKE :search OR job_applications.application_letter ILIKE :search",
                search: search_term
              )
              .distinct
  end
end
