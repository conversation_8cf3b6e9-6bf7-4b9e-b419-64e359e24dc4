require 'csv'

class SuperAdmin::AdminChatRequestsController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  before_action :set_chat_request, only: %i[show]

  def index
    @chat_requests = ChatRequest.includes(:scout, :talent)

    # Apply search
    @chat_requests = apply_search_with_associations(@chat_requests)

    # Apply filters
    @chat_requests = @chat_requests.where(status: params[:status]) if params[
      :status
    ].present?

    # Date filters
    if params[:created_after].present?
      @chat_requests =
        @chat_requests.where(
          'created_at >= ?',
          Date.parse(params[:created_after]),
        )
    end
    if params[:created_before].present?
      @chat_requests =
        @chat_requests.where(
          'created_at <= ?',
          Date.parse(params[:created_before]),
        )
    end

    # Apply sorting
    @chat_requests =
      apply_sorting(
        @chat_requests,
        %w[id status requested_at accepted_at declined_at created_at],
      )

    respond_to do |format|
      format.html do
        # Paginate
        @pagy, @chat_requests = pagy(@chat_requests, items: @page_size)

        # Set up filter options
        @filter_options = {
          status: ChatRequest.statuses.map { |key, value| [key.humanize, key] },
        }
      end
      format.csv { send_csv_data }
    end
  end

  def show
    @related_conversations =
      Conversation
        .joins(:conversation_participants)
        .where(
          conversation_participants: {
            user_id: [@chat_request.scout_id, @chat_request.talent_id],
          },
        )
        .group('conversations.id')
        .having('COUNT(conversation_participants.id) = 2')
        .includes(:users, :messages)
        .limit(5)
  end

  private

  def set_chat_request
    @chat_request = ChatRequest.find(params[:id])
  end

  def apply_search_with_associations(collection)
    return collection unless @search.present?

    # Since we already have includes(:scout, :talent), we can search on the associated models
    search_term = "%#{@search}%"
    collection
      .where(
        'users.first_name ILIKE :search OR users.last_name ILIKE :search OR users.email ILIKE :search OR
       talents_chat_requests.first_name ILIKE :search OR talents_chat_requests.last_name ILIKE :search OR
       talents_chat_requests.email ILIKE :search OR chat_requests.pitch ILIKE :search',
        search: search_term,
      )
      .joins('JOIN users ON users.id = chat_requests.scout_id')
      .joins(
        'JOIN users AS talents_chat_requests ON talents_chat_requests.id = chat_requests.talent_id',
      )
  end

  def collection_for_export
    @chat_requests
  end
end
