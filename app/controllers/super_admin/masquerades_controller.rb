module SuperAdmin
  class MasqueradesController < SuperAdmin::BaseController
    before_action :find_user, only: [:create]
    before_action :prevent_self_impersonation, only: [:create]
    before_action :prevent_admin_impersonation, only: [:create]

    # Skip all before_actions for destroy to handle impersonation ending manually
    # Skip ApplicationController before_actions
    skip_before_action :set_current_request_details, only: [:destroy]
    skip_before_action :authenticate, only: [:destroy]
    skip_before_action :check_verification, only: [:destroy]
    skip_before_action :check_impersonation_timeout, only: [:destroy]
    skip_before_action :require_onboarding_completion, only: [:destroy]
    skip_before_action :set_current_organization, only: [:destroy]
    skip_before_action :require_organization_selected, only: [:destroy]
    skip_before_action :check_impersonation_restrictions, only: [:destroy]
    # Skip SuperAdmin::BaseController before_actions
    skip_before_action :require_admin_access, only: [:destroy]

    # Use custom authentication for destroy action
    before_action :authenticate_with_impersonation_support, only: [:destroy]

    def create
      # Store original session token for restoration later
      original_session_token = cookies.signed[:session_token]
      
      # Create impersonation log for audit trail
      @impersonation_log = ImpersonationLog.create!(
        admin: Current.user,
        user: @user,
        started_at: Time.current,
        ip_address: Current.ip_address,
        user_agent: Current.user_agent
      )

      # Create new session for the target user (authentication-zero approach)
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }

      # Store impersonation state in Rails session
      session[:original_session_token] = original_session_token
      session[:impersonator_id] = Current.user.id
      session[:impersonation_log_id] = @impersonation_log.id

      # Set Current attributes for immediate use
      Current.session = session_record
      Current.user = @user
      Current.impersonator_id = session[:impersonator_id]
      Current.impersonation_log_id = session[:impersonation_log_id]

      # Redirect to the user's appropriate landing page
      redirect_path = determine_user_landing_page(@user)

      redirect_to redirect_path, notice: "Successfully started impersonating #{@user.name}. You can exit impersonation using the banner above."
    rescue StandardError => e
      Rails.logger.error "Impersonation failed: #{e.message}"
      redirect_to super_admin_users_path, alert: "Failed to impersonate user: #{e.message}"
    end

    def destroy
      unless Current.impersonating?
        redirect_to super_admin_root_path, alert: "Not currently impersonating anyone"
        return
      end

      # End the impersonation log
      if session[:impersonation_log_id]
        log = ImpersonationLog.find_by(id: session[:impersonation_log_id])
        log&.end_impersonation!
      end

      # Destroy the current impersonation session
      Current.session&.destroy

      # Restore the original session
      original_session_token = session[:original_session_token]
      if original_session_token && (session_record = Session.find_by_id(original_session_token))
        cookies.signed.permanent[:session_token] = { value: session_record.id, httponly: true }
        Current.session = session_record
        Current.user = session_record.user
        Rails.logger.debug "destroy: Restored original user = #{Current.user&.id}, superadmin? = #{Current.user&.superadmin?}"
      else
        Rails.logger.debug "destroy: Could not find original session"
        redirect_to sign_in_path, alert: "Authentication failed after ending impersonation"
        return
      end

      # Clear impersonation-related session data
      session.delete(:original_session_token)
      session.delete(:impersonator_id)
      session.delete(:impersonation_log_id)

      # Clear Current attributes
      Current.impersonator_id = nil
      Current.impersonation_log_id = nil

      redirect_to super_admin_users_path, notice: "Successfully ended impersonation"
    rescue StandardError => e
      Rails.logger.error "Failed to end impersonation: #{e.message}"
      redirect_to super_admin_root_path, alert: "Failed to end impersonation: #{e.message}"
    end

    private

    def authenticate_manually
      puts "authenticate_manually: session_token = #{cookies.signed[:session_token]}"
      puts "authenticate_manually: session[:impersonator_id] = #{session[:impersonator_id]}"
      puts "authenticate_manually: session[:impersonation_log_id] = #{session[:impersonation_log_id]}"

      if session_record = Session.find_by_id(cookies.signed[:session_token])
        Current.session = session_record
        Current.user = session_record.user

        # Set impersonation attributes from session if available
        if session[:impersonator_id] && session[:impersonation_log_id]
          Current.impersonator_id = session[:impersonator_id]
          Current.impersonation_log_id = session[:impersonation_log_id]
          puts "authenticate_manually: Set Current.impersonator_id = #{Current.impersonator_id}"
          puts "authenticate_manually: Set Current.impersonation_log_id = #{Current.impersonation_log_id}"
        else
          puts "authenticate_manually: No impersonation session variables found"
        end

        puts "authenticate_manually: Found session, user = #{Current.user&.id}, superadmin? = #{Current.user&.superadmin?}"
        puts "authenticate_manually: Current.impersonating? = #{Current.impersonating?}"
        true
      else
        puts "authenticate_manually: No session found"
        false
      end
    end

    def authenticate_with_impersonation_support
      # Set Current attributes from session if available
      if session[:impersonator_id] && session[:impersonation_log_id]
        Current.impersonator_id = session[:impersonator_id]
        Current.impersonation_log_id = session[:impersonation_log_id]
      end

      # If we're impersonating, we need to authenticate as the original user
      if Current.impersonating?
        # We're impersonating, so we need to get the original user
        if session[:impersonator_id]
          original_user = User.find_by(id: session[:impersonator_id])
          unless original_user
            redirect_to sign_in_path, alert: "Could not find original user"
            return
          end
        else
          redirect_to sign_in_path, alert: "No impersonator ID found"
          return
        end
      else
        # Normal authentication
        if session_record = Session.find_by_id(cookies.signed[:session_token])
          Current.session = session_record
          Current.user = session_record.user
        else
          redirect_to sign_in_path
          return
        end
      end

      # Ensure we have a super admin user
      # If we're impersonating, check the original user directly
      if Current.impersonating?
        original_user = User.find_by(id: session[:impersonator_id])
        unless original_user&.superadmin?
          redirect_to root_path, alert: "Access denied. Super admin privileges required."
          return
        end
      else
        unless Current.user&.superadmin?
          redirect_to root_path, alert: "Access denied. Super admin privileges required."
          return
        end
      end
    end

    def find_user
      @user = User.find_by(id: params[:user_id])
      unless @user
        redirect_to super_admin_users_path, alert: "User not found"
      end
    end

    def prevent_self_impersonation
      if @user == Current.user
        redirect_to super_admin_users_path, alert: "Cannot impersonate yourself"
      end
    end

    def prevent_admin_impersonation
      if @user.superadmin?
        redirect_to super_admin_users_path, alert: "Cannot impersonate another super admin"
      end
    end

    def determine_user_landing_page(user)
      # Determine where to redirect based on user's completion status
      if user.talent_signup_completed? || user.onboarding_completed?
        launchpad_path
      elsif user.onboarding_step == "personal"
        onboarding_personal_path
      else
        onboarding_organization_path
      end
    end

    def set_current_organization
      # Set organization context for the current user
      if Current.user.organizations.any?
        # Use the last logged in organization or the first one
        org_id = Current.user.last_logged_in_organization_id
        if org_id && Current.user.organizations.exists?(org_id)
          Current.organization = Current.user.organizations.find(org_id)
        else
          Current.organization = Current.user.organizations.first
        end
      else
        Current.organization = nil
      end
    end
  end
end
