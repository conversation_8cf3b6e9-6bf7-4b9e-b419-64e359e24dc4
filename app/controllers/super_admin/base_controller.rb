module SuperAdmin
  class BaseController < ApplicationController
    layout 'super_admin'

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected
    before_action :require_admin_access

    private

    def require_admin_access
      unless Current.user&.can_access_admin?
        redirect_to root_path,
                    alert: 'Access denied. Administrative privileges required.'
      end
    end

    # Permission checking helpers for controllers
    def require_permission(permission)
      unless Current.user&.can?(permission)
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Insufficient permissions.'
      end
    end

    def require_resource_permission(resource, action)
      permission = "#{resource}_#{action}"
      unless Current.user&.can?(permission)
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Insufficient permissions.'
        return
      end
    end

    def require_superadmin
      unless Current.user&.admin_role == :superadmin
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Super admin privileges required.'
      end
    end

    # Helper method to check if current user can perform action
    def can_perform?(action, resource = nil)
      return false unless Current.user

      if resource
        Current.user.can?("#{resource}_#{action}")
      else
        Current.user.can?(action)
      end
    end

    # Helper to get current user's admin role for views
    def current_admin_role
      Current.user&.admin_role
    end

    def current_admin_permissions
      Current.user&.admin_permissions || []
    end

    helper_method :can_perform?, :current_admin_role, :current_admin_permissions
  end
end
